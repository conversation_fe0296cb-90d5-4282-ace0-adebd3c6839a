#!/bin/bash

# Script untuk test pull Docker images dengan berbagai tag
# Jalankan di VPS/EasyPanel untuk memastikan image bisa di-pull

echo "🚀 Testing Docker Pull untuk SumoPod Images"
echo "=============================================="

# Test Server Images
echo ""
echo "📦 Testing Server Images:"
echo "-------------------------"

echo "Testing hashiif/sumopod-server:v1.1..."
if docker pull hashiif/sumopod-server:v1.1; then
    echo "✅ hashiif/sumopod-server:v1.1 - SUCCESS"
else
    echo "❌ hashiif/sumopod-server:v1.1 - FAILED"
fi

echo ""
echo "Testing hashiif/sumopod-server:v1.0..."
if docker pull hashiif/sumopod-server:v1.0; then
    echo "✅ hashiif/sumopod-server:v1.0 - SUCCESS"
else
    echo "❌ hashiif/sumopod-server:v1.0 - FAILED"
fi

echo ""
echo "Testing hashiif/sumopod-server:latest..."
if docker pull hashiif/sumopod-server:latest; then
    echo "✅ hashiif/sumopod-server:latest - SUCCESS"
else
    echo "❌ hashiif/sumopod-server:latest - FAILED"
fi

# Test Client Images
echo ""
echo "📱 Testing Client Images:"
echo "-------------------------"

echo "Testing hashiif/sumopod-client:v1.10..."
if docker pull hashiif/sumopod-client:v1.10; then
    echo "✅ hashiif/sumopod-client:v1.10 - SUCCESS"
else
    echo "❌ hashiif/sumopod-client:v1.10 - FAILED"
fi

echo ""
echo "Testing hashiif/sumopod-client:v1.9..."
if docker pull hashiif/sumopod-client:v1.9; then
    echo "✅ hashiif/sumopod-client:v1.9 - SUCCESS"
else
    echo "❌ hashiif/sumopod-client:v1.9 - FAILED"
fi

echo ""
echo "Testing hashiif/sumopod-client:latest..."
if docker pull hashiif/sumopod-client:latest; then
    echo "✅ hashiif/sumopod-client:latest - SUCCESS"
else
    echo "❌ hashiif/sumopod-client:latest - FAILED"
fi

echo ""
echo "📋 Summary:"
echo "==========="
echo "Gunakan tag yang berhasil di-pull untuk deployment di EasyPanel"
echo ""
echo "Contoh konfigurasi yang direkomendasikan:"
echo "- Server: hashiif/sumopod-server:v1.1"
echo "- Client: hashiif/sumopod-client:v1.10"
echo ""
echo "🔧 Jika semua gagal, coba:"
echo "1. docker login -u hashiif"
echo "2. Tunggu beberapa menit (rate limiting)"
echo "3. Build image lokal sebagai alternatif"
