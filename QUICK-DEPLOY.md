# 🚀 Quick Deploy Guide - SumoPod di EasyPanel

## ✅ Images Terbaru yang Siap Deploy:

- **Server**: `hashiif/sumopod-server:v1.3` (SECURE - no .env files)
- **Client**: `hashiif/sumopod-client:v1.11`

## 🔧 Langkah Cepat Deployment:

### 1. SSH ke VPS EasyPanel
```bash
ssh user@your-vps-ip
```

### 2. Login Docker Hub
```bash
docker login -u hashiif
# Masukkan password Docker Hub Anda
```

### 3. Test Pull Images
```bash
# Test pull server (SECURE version)
docker pull hashiif/sumopod-server:v1.3

# Test pull client
docker pull hashiif/sumopod-client:v1.11
```

### 4. Deploy via EasyPanel UI

**Server Configuration:**
- **Service Name**: `sumopod-server`
- **Image**: `hashiif/sumopod-server:v1.3` (SECURE)
- **Port**: `8080`
- **Environment Variables**:
```
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
BETTER_AUTH_SECRET=9uEY2Wif0Es1Ud4ZGHBmONHjbE4bypDf
BETTER_AUTH_URL=https://your-server-domain.com
BETTER_AUTH_TRUSTED_ORIGINS=https://your-client-domain.com
XENDIT_API_KEY=xnd_development_aRnvtZu0b1pF6huwa1dyMeOgIqk2QELKZr1WTRzKo2zqKs2z1spb4bb3du0PeV
XENDIT_API_URL=https://api.xendit.co/v2/invoices
XENDIT_CALLBACK_TOKEN=sGqcEXjShdnLd4S6DITIbOxbbKWxsPK0018WQb8JqtoHxlo9
PORT=8080
CORS_ORIGINS=https://your-client-domain.com
CORS_ALLOW_HEADERS=Content-Type,Authorization,X-Session-Token
CORS_ALLOW_METHODS=GET,POST,PUT,DELETE,OPTIONS
APP_NAME=sumopod-backend
EXTERNAL_ID_PREFIX=sumopod-
```

**Client Configuration:**
- **Service Name**: `sumopod-client`
- **Image**: `hashiif/sumopod-client:v1.11`
- **Port**: `3000`
- **Environment Variables**:
```
VITE_API_URL=https://your-server-domain.com
```

## 🔍 Verifikasi Deployment

### Cek Status Container
```bash
docker ps | grep sumopod
```

### Test Server
```bash
curl http://localhost:8080/health
```

### Test Client
```bash
curl http://localhost:3000
```

### Cek Logs
```bash
# Server logs
docker logs sumopod-server

# Client logs
docker logs sumopod-client
```

## ⚠️ Troubleshooting Cepat

### Jika "No such image" error:
```bash
# Login ulang
docker login -u hashiif

# Pull manual (SECURE version)
docker pull hashiif/sumopod-server:v1.3
docker pull hashiif/sumopod-client:v1.11
```

### Jika masih error, gunakan tag sebelumnya:
```bash
# ⚠️ JANGAN gunakan versi lama (VULNERABLE):
# docker pull hashiif/sumopod-server:v1.2  # CONTAINS .env FILES!
# docker pull hashiif/sumopod-server:v1.1  # CONTAINS .env FILES!

# Client alternatif (jika v1.11 bermasalah)
docker pull hashiif/sumopod-client:v1.10
```

## 🎯 Domain Configuration

**Ganti placeholder berikut dengan domain Anda:**
- `your-server-domain.com` → Domain untuk server API
- `your-client-domain.com` → Domain untuk client frontend

**Contoh:**
- Server: `api.sumopod.com`
- Client: `sumopod.com`

## 📝 Catatan Penting

1. **Environment Variables**: Pastikan semua environment variables sudah diset dengan benar
2. **CORS**: Update CORS_ORIGINS dengan domain client yang benar
3. **Database**: Pastikan DATABASE_URL bisa diakses dari container
4. **Ports**: Default server port 8080, client port 3000
5. **Health Check**: Server memiliki endpoint `/health` untuk monitoring

## 🔄 Update Deployment

Untuk update ke versi terbaru:
```bash
# Pull images terbaru (SECURE)
docker pull hashiif/sumopod-server:v1.3
docker pull hashiif/sumopod-client:v1.11

# Restart containers di EasyPanel UI
# atau via command line:
docker restart sumopod-server
docker restart sumopod-client
```

---

**✅ Setelah mengikuti langkah ini, aplikasi SumoPod seharusnya sudah running di EasyPanel VPS Anda!**
