# 🔒 Security Check Guide - Docker Images

## ⚠️ MASALAH SECURITY YANG DITEMUKAN

### 🚨 Issue: .env Files Ter-include di Docker Image

**<PERSON><PERSON><PERSON> yang ditemukan:**
- File `.env` dengan credentials sensitif ikut ter-build ke dalam Docker image `hashiif/sumopod-server:v1.2` dan versi sebelumnya
- Ini adalah **SECURITY VULNERABILITY** karena siapa saja yang bisa pull image tersebut dapat mengakses credentials

**Contoh credentials yang ter-expose:**
```
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
BETTER_AUTH_SECRET=9uEY2Wif0Es1Ud4ZGHBmONHjbE4bypDf
XENDIT_API_KEY=xnd_development_aRnvtZu0b1pF6huwa1dyMeOgIqk2QELKZr1WTRzKo2zqKs2z1spb4bb3du0PeV
```

## ✅ PERBAIKAN YANG SUDAH DILAKUKAN

### 🔧 Fix di Server Dockerfile

**Perubahan di `server/Dockerfile`:**
```dockerfile
# 5. Now copy actual code after deps are cached (excluding .env files)
COPY shared ./shared
COPY server ./server

# 5.1. Remove any .env files that might have been copied (security measure)
RUN find /app -name "*.env*" -type f -delete
```

**Image yang sudah diperbaiki:**
- ✅ `hashiif/sumopod-server:v1.3` - SECURE (no .env files)
- ❌ `hashiif/sumopod-server:v1.2` - VULNERABLE (contains .env)
- ❌ `hashiif/sumopod-server:v1.1` - VULNERABLE (contains .env)

## 🔍 Cara Cek Security Docker Image

### 1. Manual Check - Cari .env files

```bash
# Cek apakah ada .env files di image
docker run --rm hashiif/sumopod-server:v1.3 find / -name "*.env*" 2>/dev/null || echo "No .env files found - SECURE!"

# Cek di direktori spesifik
docker run --rm hashiif/sumopod-server:v1.3 ls -la /app/server/ | grep -i env || echo "No .env files in /app/server/ - SECURE!"
```

### 2. Interactive Check

```bash
# Masuk ke container untuk explore manual
docker run --rm -it --entrypoint /bin/sh hashiif/sumopod-server:v1.3

# Di dalam container, cek:
find / -name "*.env*" 2>/dev/null
ls -la /app/server/
cat /app/server/.env 2>/dev/null || echo "No .env file - SECURE!"
```

### 3. Automated Security Check Script

```bash
#!/bin/bash
# security-check.sh

IMAGE_NAME="$1"
if [ -z "$IMAGE_NAME" ]; then
    echo "Usage: $0 <image-name>"
    exit 1
fi

echo "🔍 Security Check for: $IMAGE_NAME"
echo "=================================="

# Check for .env files
ENV_FILES=$(docker run --rm $IMAGE_NAME find / -name "*.env*" 2>/dev/null)

if [ -z "$ENV_FILES" ]; then
    echo "✅ SECURE: No .env files found in image"
else
    echo "🚨 VULNERABLE: Found .env files:"
    echo "$ENV_FILES"
    
    # Show content of found .env files
    for file in $ENV_FILES; do
        echo ""
        echo "📄 Content of $file:"
        docker run --rm $IMAGE_NAME cat "$file" 2>/dev/null || echo "Cannot read file"
    done
fi
```

**Cara pakai:**
```bash
chmod +x security-check.sh
./security-check.sh hashiif/sumopod-server:v1.3
```

## 🛡️ Best Practices untuk Security

### 1. .dockerignore Configuration

Pastikan `.dockerignore` berisi:
```
.env
.env.*
*.env
.env.local
.env.development
.env.production
```

### 2. Dockerfile Security

```dockerfile
# ❌ JANGAN seperti ini (vulnerable)
COPY . .

# ✅ LAKUKAN seperti ini (secure)
COPY src ./src
COPY package.json ./
# Copy file spesifik, bukan semua

# Tambahkan security measure
RUN find /app -name "*.env*" -type f -delete
```

### 3. Environment Variables Injection

**Saat deployment, inject environment variables:**
```bash
# Via docker run
docker run -e DATABASE_URL="your-db-url" -e JWT_SECRET="your-secret" image:tag

# Via docker-compose
environment:
  - DATABASE_URL=${DATABASE_URL}
  - JWT_SECRET=${JWT_SECRET}
```

### 4. Multi-stage Build (Recommended)

```dockerfile
# Build stage
FROM node:18 AS builder
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY src ./src
RUN npm run build

# Production stage
FROM node:18-alpine AS production
WORKDIR /app
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules
# No source code, no .env files!
```

## 🚨 Action Items untuk Existing Images

### Immediate Actions:

1. **Stop using vulnerable images:**
   - ❌ `hashiif/sumopod-server:v1.2`
   - ❌ `hashiif/sumopod-server:v1.1`
   - ❌ `hashiif/sumopod-server:latest` (if it's v1.2)

2. **Use secure image:**
   - ✅ `hashiif/sumopod-server:v1.3`

3. **Rotate exposed credentials:**
   - Generate new `BETTER_AUTH_SECRET`
   - Rotate `XENDIT_API_KEY` if possible
   - Consider rotating database credentials

4. **Update deployment configs:**
   - Use environment variable injection
   - Never include .env files in images

### Long-term Security:

1. **Implement image scanning** in CI/CD
2. **Use secrets management** (Docker Secrets, Kubernetes Secrets)
3. **Regular security audits** of Docker images
4. **Principle of least privilege** for container permissions

## 📋 Security Checklist

- [ ] No .env files in Docker images
- [ ] Environment variables injected at runtime
- [ ] Sensitive data not hardcoded in source
- [ ] Regular security scans of images
- [ ] Use specific tags, not 'latest'
- [ ] Minimal base images (alpine, distroless)
- [ ] Non-root user in containers
- [ ] Regular credential rotation

## 🔄 Verification Commands

```bash
# Quick security check
docker run --rm hashiif/sumopod-server:v1.3 find / -name "*.env*" 2>/dev/null && echo "VULNERABLE!" || echo "SECURE!"

# Comprehensive check
docker run --rm hashiif/sumopod-server:v1.3 sh -c "
  echo 'Checking for sensitive files...'
  find / -name '*.env*' -o -name '*.key' -o -name '*.pem' -o -name 'id_rsa*' 2>/dev/null | head -10
  echo 'Done.'
"
```

---

**⚠️ PENTING: Selalu gunakan `hashiif/sumopod-server:v1.3` atau versi yang lebih baru untuk deployment!**
