# SumoPod Deployment Guide

This guide covers the deployment process for the SumoPod application using Docker containers.

## Prerequisites

- Docker installed on your system
- Docker Hub account (for pushing images)
- Access to ClawCloud Run platform

## Environment Variables

The application requires the following environment variables:

### Client (.env)
```
VITE_API_URL=https://your-server-domain.com
```

### Server (.env)
```
DATABASE_URL=postgresql://username:password@host:port/database
JWT_SECRET=your-jwt-secret-key
PORT=3001
```

### Root (.env)
```
# Database configuration
DATABASE_URL=postgresql://username:password@host:port/database

# JWT configuration
JWT_SECRET=your-jwt-secret-key

# Server configuration
PORT=3001

# Client configuration
VITE_API_URL=https://your-server-domain.com
```

## Docker Images

The application consists of two Docker images:

1. **Client**: `hashiif/sumopod-client:latest`
2. **Server**: `hashiif/sumopod-server:latest`

## Building Images Locally

### Build Client Image
```bash
docker build -f client/Dockerfile -t hashiif/sumopod-client:latest .
```

### Build Server Image
```bash
docker build -f server/Dockerfile -t hashiif/sumopod-server:latest .
```

## Pushing to Docker Hub

```bash
# Push client image
docker push hashiif/sumopod-client:latest

# Push server image
docker push hashiif/sumopod-server:latest
```

## Deployment on ClawCloud Run

### Current Deployment
- **URL**: https://eqospuprvxvc.ap-southeast-1.clawcloudrun.com
- **Platform**: ClawCloud Run

### Docker Hub Authentication (Important!)

To avoid rate limiting issues, authenticate with Docker Hub before pulling images:

```bash
# Login to Docker Hub
docker login

# Or login with username/token
docker login -u hashiif
```

**Note**: Docker Hub has rate limits for unauthenticated pulls (100 pulls per 6 hours). Authentication increases this limit significantly.

### Deployment Steps

1. **Authenticate with Docker Hub**
   ```bash
   docker login -u hashiif
   ```

2. **Prepare Environment Variables**
   - Ensure all required environment variables are set in your deployment platform
   - Never include .env files in the Docker images for security

3. **Deploy Server Container**
   ```bash
   # Pull and run server container
   docker run -d \
     --name sumopod-server \
     -p 3001:3001 \
     -e DATABASE_URL="your-database-url" \
     -e JWT_SECRET="your-jwt-secret" \
     -e PORT=3001 \
     hashiif/sumopod-server:latest
   ```

4. **Deploy Client Container**
   ```bash
   # Pull and run client container
   docker run -d \
     --name sumopod-client \
     -p 3000:3000 \
     -e VITE_API_URL="https://your-server-domain.com" \
     hashiif/sumopod-client:latest
   ```

## Docker Compose (Alternative)

Create a `docker-compose.yml` file for easier deployment:

```yaml
version: '3.8'

services:
  server:
    image: hashiif/sumopod-server:latest
    ports:
      - "3001:3001"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - JWT_SECRET=${JWT_SECRET}
      - PORT=3001
    restart: unless-stopped

  client:
    image: hashiif/sumopod-client:latest
    ports:
      - "3000:3000"
    environment:
      - VITE_API_URL=${VITE_API_URL}
    restart: unless-stopped
    depends_on:
      - server
```

Run with:
```bash
# Login first to avoid rate limits
docker login -u hashiif
docker-compose up -d
```

## Alternative: Build Images Locally

If you encounter persistent rate limiting issues, you can build the images locally instead of pulling from Docker Hub:

```bash
# Clone the repository and build images locally
git clone <your-repo-url>
cd sumopod-project

# Build both images
docker build -f client/Dockerfile -t hashiif/sumopod-client:latest .
docker build -f server/Dockerfile -t hashiif/sumopod-server:latest .

# Then run with docker-compose or docker run commands
docker-compose up -d
```

## Health Checks

### Server Health Check
```bash
curl http://localhost:3001/health
```

### Client Health Check
```bash
curl http://localhost:3000
```

## Troubleshooting

### Common Issues

1. **Docker Hub Rate Limit Error (HTTP 429)**
   ```
   toomanyrequests: You have reached your unauthenticated pull rate limit
   ```
   **Solution**:
   - Login to Docker Hub: `docker login -u hashiif`
   - Or wait 6 hours for rate limit reset
   - Consider using Docker Hub Pro for higher limits

2. **Environment Variables Not Set**
   - Ensure all required environment variables are properly configured
   - Check that .env files are not included in Docker images

3. **Database Connection Issues**
   - Verify DATABASE_URL is correct
   - Ensure database is accessible from the container

4. **CORS Issues**
   - Verify VITE_API_URL points to the correct server URL
   - Check server CORS configuration

5. **Port Conflicts**
   - Ensure ports 3000 and 3001 are available
   - Modify port mappings if needed

### Logs

View container logs:
```bash
# Server logs
docker logs sumopod-server

# Client logs
docker logs sumopod-client
```

## Security Considerations

1. **Environment Variables**
   - Never commit .env files to version control
   - Use secure methods to inject environment variables in production

2. **Database Security**
   - Use strong database credentials
   - Ensure database is not publicly accessible

3. **JWT Secret**
   - Use a strong, randomly generated JWT secret
   - Rotate JWT secrets regularly

## Monitoring

- Monitor container health and resource usage
- Set up logging aggregation for production deployments
- Implement alerting for critical failures

## Updates

To update the deployment:

1. Build and push new images with updated tags
2. Update the deployment configuration
3. Restart containers with new images

```bash
# Example update process
docker pull hashiif/sumopod-server:latest
docker pull hashiif/sumopod-client:latest
docker-compose down
docker-compose up -d
```
