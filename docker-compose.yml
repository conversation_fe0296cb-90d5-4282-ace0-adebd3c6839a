version: '3.8'

services:
  # Backend Service
  backend:
    build:
      context: .
      dockerfile: ./server/Dockerfile
    ports:
      - "8080:8080"
    env_file:
      - ./server/.env
    environment:
      # Override specific values for Docker if needed
      - PORT=8080
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend Service
  frontend:
    build:
      context: .
      dockerfile: ./client/Dockerfile
      args:
        # Pass build-time environment variables
        - VITE_API_BASE_URL=${VITE_API_BASE_URL:-http://localhost:8080}
        - VITE_GOLD_API_URL=${VITE_GOLD_API_URL:-https://logam-mulia-api.vercel.app/prices/anekalogam}
        - VITE_APP_NAME=${VITE_APP_NAME:-Sumopod}
    ports:
      - "3000:80"
    depends_on:
      backend:
        condition: service_healthy
