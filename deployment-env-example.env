# ===========================================
# SUMOPOD SERVER DEPLOYMENT ENVIRONMENT
# ===========================================
# Copy this file and rename to .env for deployment
# Update the values according to your deployment environment

# Database Configuration
DATABASE_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require"

# Authentication Configuration (Better Auth)
BETTER_AUTH_SECRET="9uEY2Wif0Es1Ud4ZGHBmONHjbE4bypDf"
BETTER_AUTH_URL="https://wcwtnxxyerii.ap-southeast-1.clawcloudrun.com"
BETTER_AUTH_TRUSTED_ORIGINS="https://eqospuprvxvc.ap-southeast-1.clawcloudrun.com,https://cloone-sumopod.netlify.app"

# Xendit Payment Configuration
XENDIT_API_KEY="xnd_development_aRnvtZu0b1pF6huwa1dyMeOgIqk2QELKZr1WTRzKo2zqKs2z1spb4bb3du0PeV"
XENDIT_API_URL="https://api.xendit.co/v2/invoices"
XENDIT_CALLBACK_TOKEN="sGqcEXjShdnLd4S6DITIbOxbbKWxsPK0018WQb8JqtoHxlo9"

# Server Configuration
PORT=8080

# CORS Configuration
CORS_ORIGINS="https://eqospuprvxvc.ap-southeast-1.clawcloudrun.com,https://cloone-sumopod.netlify.app"
CORS_ALLOW_HEADERS="Content-Type,Authorization,X-Session-Token"
CORS_ALLOW_METHODS="GET,POST,PUT,DELETE,OPTIONS"

# Application Configuration
APP_NAME="sumopod-backend"
EXTERNAL_ID_PREFIX="sumopod-"

# ===========================================
# DEPLOYMENT NOTES:
# ===========================================
# 1. Update BETTER_AUTH_URL to match your server domain
# 2. Update CORS_ORIGINS to include your client domain
# 3. Ensure DATABASE_URL is accessible from your deployment environment
# 4. Keep XENDIT keys secure and use production keys for production
# 5. Generate a new BETTER_AUTH_SECRET for production
