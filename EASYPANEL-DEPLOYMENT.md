# SumoPod Deployment Guide untuk EasyPanel VPS

## 🚀 Panduan Deployment di EasyPanel

### Prerequisites
- EasyPanel sudah terinstall di VPS
- Docker sudah berjalan
- Access ke EasyPanel dashboard

## 📋 Langkah-langkah Deployment

### 1. Login ke Docker Hub di EasyPanel

Jika menggunakan EasyPanel, pastika<PERSON> Docker Hub credentials sudah dikonfigurasi:

```bash
# SSH ke VPS Anda
ssh user@your-vps-ip

# Login ke Docker Hub
docker login -u hashiif
```

### 2. Konfigurasi Environment Variables

Buat file `.env` dengan konfigurasi berikut:

```env
# Database Configuration
DATABASE_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require"

# Authentication Configuration
BETTER_AUTH_SECRET="9uEY2Wif0Es1Ud4ZGHBmONHjbE4bypDf"
BETTER_AUTH_URL="https://your-server-domain.com"
BETTER_AUTH_TRUSTED_ORIGINS="https://your-client-domain.com"

# Xendit Payment Configuration
XENDIT_API_KEY="xnd_development_aRnvtZu0b1pF6huwa1dyMeOgIqk2QELKZr1WTRzKo2zqKs2z1spb4bb3du0PeV"
XENDIT_API_URL="https://api.xendit.co/v2/invoices"
XENDIT_CALLBACK_TOKEN="sGqcEXjShdnLd4S6DITIbOxbbKWxsPK0018WQb8JqtoHxlo9"

# Server Configuration
PORT=8080

# CORS Configuration
CORS_ORIGINS="https://your-client-domain.com"
CORS_ALLOW_HEADERS="Content-Type,Authorization,X-Session-Token"
CORS_ALLOW_METHODS="GET,POST,PUT,DELETE,OPTIONS"

# Application Configuration
APP_NAME="sumopod-backend"
EXTERNAL_ID_PREFIX="sumopod-"
```

### 3. Deployment via EasyPanel Dashboard

#### Option A: Menggunakan EasyPanel UI

1. **Buka EasyPanel Dashboard**
2. **Buat New Service**
3. **Pilih "Docker Image"**
4. **Konfigurasi Service:**
   - **Image**: `hashiif/sumopod-server:v1.1` (atau gunakan `v1.0` jika v1.1 bermasalah)
   - **Port**: `8080`
   - **Environment Variables**: Copy dari file `.env` di atas

#### Option B: Menggunakan Docker Compose

1. **Upload file `docker-compose.production.yml`** ke VPS
2. **Upload file `.env`** dengan konfigurasi yang sesuai
3. **Jalankan deployment:**

```bash
# Pull images terlebih dahulu dengan tag spesifik
docker pull hashiif/sumopod-server:v1.1
docker pull hashiif/sumopod-client:v1.10

# Deploy menggunakan docker-compose
docker-compose -f docker-compose.production.yml up -d
```

### 4. Verifikasi Deployment

```bash
# Cek status containers
docker ps

# Cek logs server
docker logs sumopod-server

# Cek logs client
docker logs sumopod-client

# Test server endpoint
curl http://localhost:8080/health

# Test client
curl http://localhost:3000
```

## 🔧 Troubleshooting

### Problem: "No such image" Error

**Kemungkinan Penyebab:**
1. **Docker Hub credentials tidak dikonfigurasi**
2. **Rate limiting dari Docker Hub**
3. **Network issues**

**Solusi:**

1. **Login ke Docker Hub:**
```bash
docker login -u hashiif
```

2. **Manual pull image dengan tag spesifik:**
```bash
# Coba beberapa tag yang tersedia
docker pull hashiif/sumopod-server:v1.1
# atau
docker pull hashiif/sumopod-server:v1.0
# atau
docker pull hashiif/sumopod-server:latest
```

3. **Jika masih error, build lokal:**
```bash
# Clone repository
git clone <your-repo-url>
cd sumopod-project

# Build images
docker build -f server/Dockerfile -t hashiif/sumopod-server:latest .
docker build -f client/Dockerfile -t hashiif/sumopod-client:latest .
```

### Problem: Environment Variables tidak terbaca

**Solusi:**
1. Pastikan file `.env` ada di direktori yang sama dengan docker-compose
2. Restart containers setelah mengubah environment variables
3. Cek logs untuk error messages

### Problem: Database Connection Error

**Solusi:**
1. Pastikan DATABASE_URL benar
2. Cek network connectivity ke database
3. Pastikan database credentials masih valid

### Problem: CORS Error

**Solusi:**
1. Update `CORS_ORIGINS` dengan domain yang benar
2. Pastikan `BETTER_AUTH_TRUSTED_ORIGINS` sesuai
3. Restart server setelah mengubah CORS settings

## 📝 Konfigurasi Domain

Jika menggunakan custom domain:

1. **Update Environment Variables:**
```env
BETTER_AUTH_URL="https://api.yourdomain.com"
BETTER_AUTH_TRUSTED_ORIGINS="https://yourdomain.com"
CORS_ORIGINS="https://yourdomain.com"
```

2. **Setup Reverse Proxy di EasyPanel:**
   - Server: `api.yourdomain.com` → `localhost:8080`
   - Client: `yourdomain.com` → `localhost:3000`

## 🔒 Security Notes

1. **Ganti BETTER_AUTH_SECRET** untuk production
2. **Gunakan HTTPS** untuk semua endpoints
3. **Secure database credentials**
4. **Update XENDIT keys** untuk production
5. **Restrict CORS origins** hanya ke domain yang diperlukan

## 📊 Monitoring

```bash
# Monitor resource usage
docker stats

# View logs in real-time
docker logs -f sumopod-server
docker logs -f sumopod-client

# Check container health
docker inspect sumopod-server | grep Health
```

## 🔄 Updates

Untuk update aplikasi:

```bash
# Pull specific version images
docker pull hashiif/sumopod-server:v1.1
docker pull hashiif/sumopod-client:v1.10

# Restart services
docker-compose -f docker-compose.production.yml down
docker-compose -f docker-compose.production.yml up -d
```
