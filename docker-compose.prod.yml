version: '3.8'

services:
  # Backend Service
  backend:
    image: hashiif/sumopod-server:latest
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - BETTER_AUTH_SECRET=${BETTER_AUTH_SECRET}
      - BETTER_AUTH_URL=${BETTER_AUTH_URL}
      - BETTER_AUTH_TRUSTED_ORIGINS=${BETTER_AUTH_TRUSTED_ORIGINS}
      - XENDIT_API_KEY=${XENDIT_API_KEY}
      - XENDIT_API_URL=${XENDIT_API_URL:-https://api.xendit.co/v2/invoices}
      - XENDIT_CALLBACK_TOKEN=${XENDIT_CALLBACK_TOKEN}
      - PORT=8080
      - CORS_ORIGINS=${CORS_ORIGINS}
      - CORS_ALLOW_HEADERS=${CORS_ALLOW_HEADERS:-Content-Type,Authorization,X-Session-Token}
      - CORS_ALLOW_METHODS=${CORS_ALLOW_METHODS:-GET,POST,PUT,DELETE,OPTIONS}
      - APP_NAME=${APP_NAME:-sumopod-backend}
      - EXTERNAL_ID_PREFIX=${EXTERNAL_ID_PREFIX:-sumopod-}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend Service
  frontend:
    image: hashiif/sumopod-client:latest
    ports:
      - "80:80"
    depends_on:
      backend:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
